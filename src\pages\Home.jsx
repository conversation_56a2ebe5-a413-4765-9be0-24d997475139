import React, { useEffect, useState } from "react";
import Navbar from "../components/Navbar";
import api from "../services/api/api";
import { useAuthGuard } from "../hooks/useAuthGuard";
import { Link, useNavigate } from "react-router-dom";
import CreateStateModal from "../components/states/CreateStateModal";
import { showErrorToast } from "../utils/showErrorToast";
import GlobalWarStats from "../components/analytics/GlobalWarStats";
import WarTimeline from "../components/analytics/WarTimeline";
import { MapPin, Users, Plane, Clock, ArrowRight, Vote } from "lucide-react";
import { stateService } from "../services/api/state.service";
import GlobalTravelStatus from "../components/travel/GlobalTravelStatus";
import { travelService } from "../services/api/travel.service";
import useTravelCountdown from "../hooks/useTravelCountdown";
import SearchableModal from "../components/common/SearchableModal";
import useElectionStore from "../store/useElectionStore";
import ElectionCountdown from "../components/elections/ElectionCountdown";

export default function Home() {
  useAuthGuard();
  const navigate = useNavigate();
  const { fetchActiveElection, activeElection, loading: electionLoading } = useElectionStore();

  const [stats, setStats] = useState({
    regions: 0,
    population: 0,
    activeWars: 0,
    states: 0,
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [resources, setResources] = useState({
    money: 0,
    energy: 0,
    food: 0,
  });
  const [myState, setMyState] = useState(null);
  const [activeWars, setActiveWars] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isCreateStateModalOpen, setIsCreateStateModalOpen] = useState(false);
  const [currentTravel, setCurrentTravel] = useState(null);
  const [travelLoading, setTravelLoading] = useState(false);

  // Modal states
  const [regionsModalOpen, setRegionsModalOpen] = useState(false);
  const [playersModalOpen, setPlayersModalOpen] = useState(false);
  const [regionsData, setRegionsData] = useState([]);
  const [regionsDataCount, setRegionsDataCount] = useState([]);
  const [playersData, setPlayersData] = useState([]);
  const [playersDataCount, setPlayersDataCount] = useState([]);
  const [regionsLoading, setRegionsLoading] = useState(false);
  const [playersLoading, setPlayersLoading] = useState(false);
  
  const [states, setStates] = useState([]);
  const [statesCount, setStatesCount] = useState([]);
  const [statesModalOpen, setStatesModalOpen] = useState(false);
  const [statesLoading, setStatesLoading] = useState(false);

  // Function to fetch state data - can be called independently
  const fetchStateData = async () => {
    try {
      const stateResponse = await stateService.getUserState();
      setMyState(stateResponse);
      return stateResponse;
    } catch (error) {
      setMyState(null);
      return null;
    }
  };

  // Function to fetch travel data
  const fetchTravelData = async () => {
    try {
      setTravelLoading(true);
      const travel = await travelService.getCurrentTravel();
      setCurrentTravel(travel);
      return travel;
    } catch (error) {
      setCurrentTravel(null);
      return null;
    } finally {
      setTravelLoading(false);
    }
  };

  // Function to fetch election data for user's state
  const fetchElectionData = async (stateId) => {
    if (!stateId) return null;

    try {
      const election = await fetchActiveElection(stateId);
      return election;
    } catch (error) {
      console.error("Error fetching election data:", error);
      return null;
    }
  };

  // Function to fetch regions data for modal
  const fetchRegionsData = async () => {
    setRegionsLoading(true);
    try {
      const response = await api.get("/regions");
      setRegionsData(response.data);
    } catch (error) {
      console.error("Error fetching regions:", error);
      showErrorToast("Failed to load regions data");
    } finally {
      setRegionsLoading(false);
    }
  };

  const fetchRegionsDataCount = async () => {
    setRegionsLoading(true);
    try {
      const response = await api.get("/regions/count");
      setRegionsDataCount(response.data);
    } catch (error) {
      console.error("Error fetching regions:", error);
      showErrorToast("Failed to load regions data");
    } finally {
      setRegionsLoading(false);
    }
  };

  // Function to fetch players/states data for modal
  const fetchPlayersData = async () => {
    setPlayersLoading(true);
    try {
      const response = await api.get("/users");
      setPlayersData(response.data);
    } catch (error) {
      console.error("Error fetching players:", error);
      showErrorToast("Failed to load players data");
    } finally {
      setPlayersLoading(false);
    }
  };

  const fetchPlayersDataCount = async () => {
    setPlayersLoading(true);
    try {
      const response = await api.get("/users/count");
      setPlayersDataCount(response.data);
    } catch (error) {
      console.error("Error fetching players:", error);
      showErrorToast("Failed to load players data");
    } finally {
      setPlayersLoading(false);
    }
  };

  // Function to fetch players/states data for modal
  const fetchStatesData = async () => {
    setStatesLoading(true);
    try {
      const response = await stateService.getAllStates()
      setStates(response);
    } catch (error) {
      console.error("Error fetching players:", error);
      showErrorToast("Failed to load players data");
    } finally {
      setStatesLoading(false);
    }
  };

  const fetchStatesDataCount = async () => {
    setStatesLoading(true);
    try {
      const response = await stateService.getAllStatesCount()
      setStatesCount(response);
    } catch (error) {
      console.error("Error fetching players:", error);
      showErrorToast("Failed to load players data");
    } finally {
      setStatesLoading(false);
    }
  };

  // Handle item click for both regions and players
  const handleItemClick = (item, type) => {
    if (type === "region") {
      navigate(`/regions/${item.id}`);
    } if(type === "player") {
      navigate(`/users/${item.id}`);
    } if(type === "state") {
      navigate(`/states/${item.id}`);
    }
  };

  // Render function for region items
  const renderRegionItem = (region) => (
    <div>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-white">{region.name}</h3>
        <span
          className={`text-sm font-medium ${getStatusColor(
            region.status || "Active"
          )}`}
        >
          {region.status || "Active"}
        </span>
      </div>
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-400">Population:</span>
          <span className="text-white">
            {region.population?.toLocaleString() || "N/A"}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-400">State:</span>
          <span className="text-white">
            {region.state?.name || "Independent"}
          </span>
        </div>
        {region.location && (
          <div className="flex justify-between">
            <span className="text-gray-400">Location:</span>
            <span className="text-white">{region.location}</span>
          </div>
        )}
      </div>
    </div>
  );

  // Render function for player items
  const renderPlayerItem = (player) => (
    <div>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center text-lg text-neonBlue">
                            {player?.avatarUrl ? (
                  <img
                    src={player.avatarUrl}
                    alt="Profile Avatar"
                    className="w-full h-full object-cover rounded-full"
                  />
                ) : (
                  player?.username?.charAt(0).toUpperCase() || "U"
                )}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">
              {player.username}
            </h3>
            <div className="flex items-center gap-2 text-sm">
              <span className="text-gray-400">Level:</span>
              <span className="text-white font-medium">
                {player.level || 0}
              </span>
            </div>
          </div>
        </div>
        {player.isPremium && (
          <span className="text-yellow-400 text-xs px-2 py-0.5 bg-yellow-900/30 rounded-full">
            PREMIUM
          </span>
        )}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div className="flex justify-between md:flex-col">
          <span className="text-gray-400">Strength:</span>
          <span className="text-white font-medium">{player.strength || 0}</span>
        </div>
        <div className="flex justify-between md:flex-col">
          <span className="text-gray-400">Intelligence:</span>
          <span className="text-white font-medium">
            {player.intelligence || 0}
          </span>
        </div>
        <div className="flex justify-between md:flex-col">
          <span className="text-gray-400">Endurance:</span>
          <span className="text-white font-medium">
            {player.endurance || 0}
          </span>
        </div>
      </div>
    </div>
  );

  // Render function for state items
  const renderStateItem = (state) => (
    <div>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          {/* <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center text-lg text-neonBlue">
            {state.name}
          </div> */}
          <div>
            <h3 className="text-lg font-semibold text-white">
              {state.name}
            </h3>
            <div className="flex items-center gap-2 text-sm">
              <span className="text-gray-400">Leader:</span>
              <span className="text-white font-medium">
                {state?.leader?.username || ""}
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <span className="text-gray-400">Regions:</span>
              <span className="text-white font-medium">
                {state.regions?.length || 0}
              </span>
            </div>
          </div>
        </div>
      </div>
      {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div className="flex justify-between md:flex-col">
          <span className="text-gray-400">Strength:</span>
          <span className="text-white font-medium">{state.strength || 0}</span>
        </div>
        <div className="flex justify-between md:flex-col">
          <span className="text-gray-400">Intelligence:</span>
          <span className="text-white font-medium">
            {state.intelligence || 0}
          </span>
        </div>
        <div className="flex justify-between md:flex-col">
          <span className="text-gray-400">Endurance:</span>
          <span className="text-white font-medium">
            {state.endurance || 0}
          </span>
        </div>
      </div> */}
    </div>
  );

  useEffect(() => {
    const fetchData = async () => {
      try {
        // First, fetch state data and travel data
        const stateData = await fetchStateData();
        await fetchTravelData();
        await fetchStatesDataCount();
        await fetchRegionsDataCount();
        await fetchPlayersDataCount();

        // Fetch election data if user has a state
        if (stateData && stateData.id) {
          await fetchElectionData(stateData.id);
        }

        // Then fetch other data with proper error handling
        // let regions = [];
        // try {
        //   const regionsResponse = await api.get("/regions");
        //   regions = regionsResponse.data;
        // } catch (error) {
        //   console.error("Error fetching regions:", error);
        // }

        // Calculate total population if regions data exists
        // const totalPopulation =
        //   regions.length > 0
        //     ? regions.reduce((sum, region) => sum + (region.population || 0), 0)
        //     : 0;

        // Fetch active wars with error handling
        let wars = [];
        try {
          const warsResponse = await api.get("/wars/active");
          wars = warsResponse.data;
          setActiveWars(wars);
        } catch (error) {
          console.error("Error fetching wars:", error);
        }

        // Update stats
        setStats({
          regions: 0,
          population: 0,
          activeWars: wars.length,
          states: states.length,
        });
      } catch (error) {
        console.error("Failed to fetch data:", error);
        showErrorToast("Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // This function will be called after successful state creation
  const refreshStateData = async () => {
    try {
      const state = await fetchStateData();
      return state;
    } catch (error) {
      console.error("Failed to refresh state data:", error);
      return null;
    }
  };

  const onViewRegions = () => {
    setRegionsModalOpen(true);
    if(regionsData.length === 0){
      fetchRegionsData();
    }
  };

  const onViewPlayers = () => {
    setPlayersModalOpen(true);
    if(playersData.length === 0){
      fetchPlayersData();
    }
  };

  const onViewWars = () => {
    navigate("/wars");
  };
  const onViewStates = () => {
    setStatesModalOpen(true);
    if(states.length === 0){
      fetchStatesData();
    }
  };

  const closeRegionsModal = () => {
    setRegionsModalOpen(false);
    setRegionsData([]);
  };

  const closePlayersModal = () => {
    setPlayersModalOpen(false);
    setPlayersData([]);
  };

  const closeStatesModal = () => {
    setStatesModalOpen(false);
  };

  const getStatusColor = (status) => {
    if (!status) return "text-gray-400";
    switch (status.toLowerCase()) {
      case "controlled":
        return "text-green-400";
      case "contested":
        return "text-yellow-400";
      case "expanding":
        return "text-blue-400";
      case "active":
        return "text-green-400";
      case "online":
        return "text-emerald-400";
      case "offline":
        return "text-red-400";
      default:
        return "text-gray-400";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-neonBlue text-xl">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Welcome Section */}
        {/* <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <h1 className="text-2xl font-bold text-neonBlue mb-2">
            Welcome back, Commander
          </h1>
          <p className="text-gray-400">
            Here's an overview of your empire's status
          </p>
        </div> */}

        {/* Travel Status Section */}
        {currentTravel && currentTravel.status === "in_progress" && (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Plane className="w-5 h-5 text-blue-400 mr-2" />
                Travel Status
              </h2>
              <Link
                to="/travel/permissions"
                className="text-blue-400 hover:text-blue-300 text-sm flex items-center"
              >
                Manage Travel <ArrowRight className="w-4 h-4 ml-1" />
              </Link>
            </div>
            <GlobalTravelStatus />
          </div>
        )}

        {/* Election Status Section */}
        {activeElection && activeElection.status === "active" && (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Vote className="w-5 h-5 text-purple-400 mr-2" />
                Active Election
              </h2>
              <Link
                to="/elections"
                className="text-purple-400 hover:text-purple-300 text-sm flex items-center"
              >
                View Election <ArrowRight className="w-4 h-4 ml-1" />
              </Link>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 mb-1">
                    Election in progress for <span className="text-white font-medium">{activeElection.state?.name}</span>
                  </p>
                  <p className="text-sm text-gray-400">
                    {activeElection.candidates?.length || 0} candidates • {activeElection.totalVotes || 0} votes cast
                  </p>
                </div>
              </div>
              {activeElection.endTime && (
                <ElectionCountdown endTime={activeElection.endTime} />
              )}
            </div>
          </div>
        )}

        {/* Travel Quick Access */}
        {(!currentTravel || currentTravel.status !== "in_progress") && (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Plane className="w-5 h-5 text-blue-400 mr-2" />
                Travel
              </h2>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 mb-2">Plan your next journey</p>
                <p className="text-sm text-gray-400">
                  Travel to different regions to explore new opportunities
                </p>
              </div>
              {/* <Link
                to="/travel/permissions"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
              >
                <Plane className="w-4 h-4 mr-2" />
                Start Travel
              </Link> */}
            </div>
          </div>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className="bg-gray-800 rounded-lg shadow-lg p-6 cursor-pointer hover:bg-gray-700 transition-all duration-200 hover:shadow-xl hover:scale-105"
           onClick={onViewStates}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-gray-400">States</h3>
              <span className="text-neonBlue text-2xl">🗺️</span>
            </div>
            <p className="text-3xl font-bold text-white">
              {statesCount || 0}
            </p>
            <p className="text-sm text-neonBlue mt-2 opacity-75">
              Click to view all states
            </p>
          </div>

          <div
            className="bg-gray-800 rounded-lg shadow-lg p-6 cursor-pointer hover:bg-gray-700 transition-all duration-200 hover:shadow-xl hover:scale-105"
            onClick={onViewRegions}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-gray-400">Regions</h3>
              <span className="text-neonBlue text-2xl">🌍</span>
            </div>
            <p className="text-3xl font-bold text-white">{regionsDataCount || 0}</p>
            <p className="text-sm text-neonBlue mt-2 opacity-75">
              Click to view all regions
            </p>
          </div>

          <div
            className="bg-gray-800 rounded-lg shadow-lg p-6 cursor-pointer hover:bg-gray-700 transition-all duration-200 hover:shadow-xl hover:scale-105"
            onClick={onViewPlayers}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-gray-400">Total Population</h3>
              <span className="text-neonBlue text-2xl">👥</span>
            </div>
            <p className="text-3xl font-bold text-white">
              {playersDataCount || 0}
            </p>
            <p className="text-sm text-neonBlue mt-2 opacity-75">
              Click to view all players
            </p>
          </div>

          <div
            className="bg-gray-800 rounded-lg shadow-lg p-6 cursor-pointer hover:bg-gray-700 transition-all duration-200 hover:shadow-xl hover:scale-105"
            onClick={onViewWars}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-gray-400">Active Wars</h3>
              <span className="text-neonBlue text-2xl">⚔️</span>
            </div>
            <p className="text-3xl font-bold text-white">{stats.activeWars}</p>
            <p className="text-sm text-neonBlue mt-2 opacity-75">
              Click to view all wars
            </p>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="mb-8">
          <GlobalWarStats />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">
              Recent Activity
            </h2>
            <div className="space-y-4">
              {recentActivity.length > 0 ? (
                recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div
                      className={`w-2 h-2 rounded-full mt-2 ${
                        activity.type === "conquest"
                          ? "bg-neonBlue"
                          : activity.type === "resource"
                          ? "bg-yellow-400"
                          : "bg-red-400"
                      }`}
                    ></div>
                    <div>
                      <p className="text-white">{activity.description}</p>
                      <p className="text-sm text-gray-400">
                        {new Date(activity.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-400">No recent activity to display.</p>
              )}
            </div>
          </div>

          {/* State Information */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-white">My State</h2>
              {myState && (
                <Link
                  to={`/states/${myState.id}`}
                  className="text-blue-400 hover:text-blue-300 text-sm"
                >
                  View Details
                </Link>
              )}
            </div>

            {myState ? (
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center text-xl text-neonBlue">
                    {myState.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-3">
                    <h3 className="text-lg font-medium text-white">
                      {myState.name}
                    </h3>
                    <p className="text-gray-400 text-sm">
                      Leader: {myState.leader?.username || "Unknown"}
                    </p>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Regions:</span>
                    <span className="text-white">
                      {myState.regions?.length || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Treasury:</span>
                    <span className="text-white">
                      {myState.treasury?.toLocaleString() || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Status:</span>
                    <span
                      className={`${
                        myState.isActive ? "text-green-400" : "text-red-400"
                      }`}
                    >
                      {myState.isActive ? "Active" : "Inactive"}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400 mb-4">
                  You don't belong to any state yet.
                </p>
                <button
                  onClick={() => setIsCreateStateModalOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded inline-block"
                >
                  Create a State
                </button>
              </div>
            )}
          </div>

          {/* Active Wars */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-white">Active Wars</h2>
              <Link
                to="/wars"
                className="text-red-400 hover:text-red-300 text-sm"
              >
                View All Wars
              </Link>
            </div>

            {activeWars.length > 0 ? (
              <div className="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                <div className="space-y-4 pr-2">
                  {activeWars.slice(0, 5).map((war) => (
                    <div
                      key={war.id}
                      className="border-b border-gray-700 pb-4 last:border-0 last:pb-0"
                    >
                      <h3 className="text-lg font-medium text-white mb-2">
                        {war.warType} War
                      </h3>
                      <div className="space-y-1 mb-2">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Attacker:</span>
                          <p className="text-gray-300 mb-1">
                            <Link
                              to={`/regions/${war.attackerRegion.id}`}
                              className="text-neonBlue hover:text-blue-400"
                            >
                              {war.attackerRegion.name}
                            </Link>
                          </p>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Defender:</span>
                          <p className="text-gray-300 mb-1">
                            <Link
                              to={`/regions/${war.defenderRegion.id}`}
                              className="text-neonBlue hover:text-blue-400"
                            >
                              {war.defenderRegion.name}
                            </Link>
                          </p>
                        </div>
                      </div>
                      <Link
                        to={`/wars/${war.id}`}
                        className="text-red-400 hover:text-red-300 text-sm"
                      >
                        View War Details
                      </Link>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400 mb-4">No active wars.</p>
                <Link
                  to="/wars/new"
                  className="bg-red-600 hover:bg-red-700 !text-white px-4 py-2 rounded inline-block [&]:text-white [&:hover]:text-white"
                >
                  Declare War
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* War Timeline */}
        <div className="mt-6 bg-gray-800 rounded-lg shadow-lg p-6">
          <WarTimeline limit={5} />
        </div>

        {/* Resource Overview */}
        {/* <div className="mt-6 bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">
            Resource Overview
          </h2>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-gray-400">Money</span>
                <span className="text-white">
                  {resources.money.toLocaleString()}
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-yellow-400 h-2 rounded-full"
                  style={{
                    width: `${Math.min(
                      100,
                      (resources.money / 1000000) * 100
                    )}%`,
                  }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-gray-400">Energy</span>
                <span className="text-white">
                  {resources.energy.toLocaleString()}
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-400 h-2 rounded-full"
                  style={{
                    width: `${Math.min(
                      100,
                      (resources.energy / 500000) * 100
                    )}%`,
                  }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-gray-400">Food</span>
                <span className="text-white">
                  {resources.food.toLocaleString()}
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-green-400 h-2 rounded-full"
                  style={{
                    width: `${Math.min(100, (resources.food / 300000) * 100)}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div> */}
      </div>

      {/* Regions Modal */}
      <SearchableModal
        isOpen={regionsModalOpen}
        onClose={closeRegionsModal}
        title="All Regions"
        icon={MapPin}
        data={regionsData}
        loading={regionsLoading}
        onItemClick={(item) => handleItemClick(item, "region")}
        renderItem={renderRegionItem}
        searchPlaceholder="Search regions by name"
      />

      {/* Players Modal */}
      <SearchableModal
        isOpen={playersModalOpen}
        onClose={closePlayersModal}
        title="All Players"
        icon={Users}
        data={playersData}
        loading={playersLoading}
        onItemClick={(item) => handleItemClick(item, "player")}
        renderItem={renderPlayerItem}
        searchPlaceholder="Search players by username"
      />
      {/* States Modal */}
      <SearchableModal
        isOpen={statesModalOpen}
        onClose={closeStatesModal}
        title="All States"
        icon={Users}
        data={states}
        loading={statesLoading}
        onItemClick={(item) => handleItemClick(item, "state")}
        renderItem={renderStateItem}
        searchPlaceholder="Search players by state name"
      />

      {/* Create State Modal */}
      <CreateStateModal
        isOpen={isCreateStateModalOpen}
        onClose={() => setIsCreateStateModalOpen(false)}
        onSuccess={(newState) => {
          // Use the state data from the response or refresh
          if (newState) {
            setMyState(newState);
          } else {
            // If no state data in response, refresh it
            refreshStateData();
          }
          setIsCreateStateModalOpen(false);
        }}
      />
    </div>
  );
}
